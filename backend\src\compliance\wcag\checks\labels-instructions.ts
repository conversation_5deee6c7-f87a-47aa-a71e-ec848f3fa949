/**
 * WCAG-030: Labels or Instructions Check
 * Success Criterion: 3.3.2 Labels or Instructions (Level A)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface FormControlAnalysis {
  element: string;
  type: string;
  selector: string;
  hasLabel: boolean;
  hasAriaLabel: boolean;
  hasAriaLabelledby: boolean;
  hasAriaDescribedby: boolean;
  hasPlaceholder: boolean;
  hasTitle: boolean;
  hasInstructions: boolean;
  isRequired: boolean;
  labelText?: string;
  placeholderText?: string;
  instructionText?: string;
  isAccessible: boolean;
  issues: string[];
}

interface LabelsInstructionsAnalysis {
  formControls: FormControlAnalysis[];
  totalControls: number;
  controlsWithLabels: number;
  controlsWithInstructions: number;
  controlsWithIssues: number;
  requiredControls: number;
  requiredControlsWithInstructions: number;
}

export interface LabelsInstructionsConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableAISemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class LabelsInstructionsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  async performCheck(config: LabelsInstructionsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: LabelsInstructionsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableAISemanticValidation: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-030',
      'Labels or Instructions',
      'understandable',
      0.0815,
      'A',
      enhancedConfig,
      this.executeLabelsInstructionsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with form label analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-030',
        ruleName: 'Labels or Instructions',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.9,
          checkType: 'form-label-analysis',
          labelAssociationValidation: true,
          instructionDetection: true,
          ariaLabelValidation: true,
          formAccessibilityAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.85,
        maxEvidenceItems: 40,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'labels-instructions-analysis',
        confidence: 0.80,
        additionalData: {
          checkType: 'form-accessibility',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeLabelsInstructionsCheck(
    page: Page,
    config: LabelsInstructionsConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze form controls and their labels/instructions
    const labelsAnalysis = await page.evaluate((): LabelsInstructionsAnalysis => {
      const formControlSelectors = [
        'input:not([type="hidden"]):not([type="submit"]):not([type="button"]):not([type="reset"])',
        'select',
        'textarea',
        '[role="textbox"]',
        '[role="combobox"]',
        '[role="listbox"]',
        '[role="spinbutton"]',
        '[role="slider"]'
      ];

      const formControls = document.querySelectorAll(formControlSelectors.join(', '));
      const controlAnalysis: FormControlAnalysis[] = [];

      formControls.forEach((control, index) => {
        const tagName = control.tagName.toLowerCase();
        const type = control.getAttribute('type') || tagName;
        const id = control.getAttribute('id');
        const name = control.getAttribute('name');
        
        // Check for various labeling methods
        const hasLabel = !!document.querySelector(`label[for="${id}"]`) || 
                        !!control.closest('label');
        const hasAriaLabel = !!control.getAttribute('aria-label');
        const hasAriaLabelledby = !!control.getAttribute('aria-labelledby');
        const hasAriaDescribedby = !!control.getAttribute('aria-describedby');
        const hasPlaceholder = !!control.getAttribute('placeholder');
        const hasTitle = !!control.getAttribute('title');
        
        // Get label text
        let labelText = '';
        if (hasLabel) {
          const labelElement = document.querySelector(`label[for="${id}"]`) || 
                              control.closest('label');
          labelText = labelElement?.textContent?.trim() || '';
        } else if (hasAriaLabel) {
          labelText = control.getAttribute('aria-label') || '';
        } else if (hasAriaLabelledby) {
          const labelledbyId = control.getAttribute('aria-labelledby');
          const labelledbyElement = labelledbyId ? document.getElementById(labelledbyId) : null;
          labelText = labelledbyElement?.textContent?.trim() || '';
        }

        // Check for instructions
        let instructionText = '';
        let hasInstructions = false;
        
        if (hasAriaDescribedby) {
          const describedbyId = control.getAttribute('aria-describedby');
          const describedbyElement = describedbyId ? document.getElementById(describedbyId) : null;
          instructionText = describedbyElement?.textContent?.trim() || '';
          hasInstructions = !!instructionText;
        }

        // Look for nearby instruction text
        if (!hasInstructions) {
          const parent = control.parentElement;
          const siblings = parent ? Array.from(parent.children) : [];
          const instructionElements = siblings.filter(el => 
            el.classList.contains('help-text') ||
            el.classList.contains('instruction') ||
            el.classList.contains('hint') ||
            el.tagName.toLowerCase() === 'small' ||
            el.getAttribute('role') === 'note'
          );
          
          if (instructionElements.length > 0) {
            instructionText = instructionElements[0].textContent?.trim() || '';
            hasInstructions = !!instructionText;
          }
        }

        const isRequired = control.hasAttribute('required') || 
                          control.getAttribute('aria-required') === 'true';

        // Determine accessibility and issues
        const issues: string[] = [];
        let isAccessible = true;

        if (!hasLabel && !hasAriaLabel && !hasAriaLabelledby) {
          issues.push('Missing accessible label');
          isAccessible = false;
        }

        if (isRequired && !hasInstructions && !labelText.toLowerCase().includes('required')) {
          issues.push('Required field without clear indication');
        }

        if (type === 'password' && !hasInstructions) {
          issues.push('Password field without format instructions');
        }

        if ((type === 'email' || type === 'tel' || type === 'url') && !hasInstructions) {
          issues.push('Specialized input without format instructions');
        }

        controlAnalysis.push({
          element: tagName,
          type,
          selector: `${tagName}:nth-of-type(${index + 1})`,
          hasLabel,
          hasAriaLabel,
          hasAriaLabelledby,
          hasAriaDescribedby,
          hasPlaceholder,
          hasTitle,
          hasInstructions,
          isRequired,
          labelText,
          placeholderText: control.getAttribute('placeholder') || undefined,
          instructionText: instructionText || undefined,
          isAccessible,
          issues,
        });
      });

      const totalControls = controlAnalysis.length;
      const controlsWithLabels = controlAnalysis.filter(c => c.hasLabel || c.hasAriaLabel || c.hasAriaLabelledby).length;
      const controlsWithInstructions = controlAnalysis.filter(c => c.hasInstructions).length;
      const controlsWithIssues = controlAnalysis.filter(c => c.issues.length > 0).length;
      const requiredControls = controlAnalysis.filter(c => c.isRequired).length;
      const requiredControlsWithInstructions = controlAnalysis.filter(c => 
        c.isRequired && (c.hasInstructions || c.labelText?.toLowerCase().includes('required'))
      ).length;

      return {
        formControls: controlAnalysis,
        totalControls,
        controlsWithLabels,
        controlsWithInstructions,
        controlsWithIssues,
        requiredControls,
        requiredControlsWithInstructions,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = labelsAnalysis.totalControls;

    if (totalElements === 0) {
      // No form controls found
      evidence.push({
        type: 'info',
        description: 'No form controls found on page',
        value: 'Page contains no form controls requiring labels or instructions',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            totalControls: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Ensure any future form controls include proper labels and instructions'],
      };
    }

    // Analyze controls with issues
    const controlsWithIssues = labelsAnalysis.formControls.filter(control => control.issues.length > 0);
    
    if (controlsWithIssues.length > 0) {
      const failureRate = controlsWithIssues.length / totalElements;
      score = Math.max(0, Math.round(100 * (1 - failureRate)));

      controlsWithIssues.forEach((control) => {
        issues.push(`${control.element}[${control.type}]: ${control.issues.join(', ')}`);
        
        evidence.push({
          type: 'error',
          description: `Form control missing labels or instructions`,
          value: `${control.element}[type="${control.type}"] - ${control.issues.join(', ')}`,
          selector: control.selector,
          elementCount: 1,
          affectedSelectors: [control.selector],
          severity: 'error',
          fixExample: {
            before: this.generateBeforeExample(control),
            after: this.generateAfterExample(control),
            description: 'Add proper labels and instructions for form controls',
            codeExample: `
<!-- Method 1: Using label element -->
<label for="email">Email Address (required)</label>
<input type="email" id="email" required aria-describedby="email-help">
<div id="email-help">Enter a valid email <NAME_EMAIL></div>

<!-- Method 2: Using aria-label -->
<input type="password" aria-label="Password" aria-describedby="pwd-help">
<div id="pwd-help">Password must be at least 8 characters long</div>

<!-- Method 3: Using aria-labelledby -->
<div id="phone-label">Phone Number</div>
<input type="tel" aria-labelledby="phone-label" aria-describedby="phone-help">
<div id="phone-help">Format: (*************</div>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/labels-or-instructions.html',
              'https://webaim.org/techniques/forms/controls',
              'https://www.w3.org/WAI/tutorials/forms/labels/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              controlType: control.type,
              hasLabel: control.hasLabel,
              hasAriaLabel: control.hasAriaLabel,
              hasInstructions: control.hasInstructions,
              isRequired: control.isRequired,
              issues: control.issues,
            },
          },
        });
      });
    }

    // Add summary evidence
    evidence.push({
      type: score === 100 ? 'info' : 'warning',
      description: 'Form controls labels and instructions analysis',
      value: `${totalElements} form controls: ${labelsAnalysis.controlsWithLabels} with labels, ${controlsWithIssues.length} with issues`,
      selector: 'form, body',
      elementCount: totalElements,
      affectedSelectors: ['input', 'select', 'textarea'],
      severity: score === 100 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: totalElements,
        checkSpecificData: {
          totalControls: labelsAnalysis.totalControls,
          controlsWithLabels: labelsAnalysis.controlsWithLabels,
          controlsWithInstructions: labelsAnalysis.controlsWithInstructions,
          controlsWithIssues: labelsAnalysis.controlsWithIssues,
          requiredControls: labelsAnalysis.requiredControls,
          requiredControlsWithInstructions: labelsAnalysis.requiredControlsWithInstructions,
          labelingRate: totalElements > 0 ? (labelsAnalysis.controlsWithLabels / totalElements * 100).toFixed(1) : '0',
        },
      },
    });

    // Generate recommendations
    if (controlsWithIssues.length > 0) {
      recommendations.push('Provide accessible labels for all form controls');
      recommendations.push('Use label elements, aria-label, or aria-labelledby for labeling');
      recommendations.push('Include clear instructions for required fields and input formats');
      recommendations.push('Use aria-describedby to associate instructions with form controls');
    } else {
      recommendations.push('Continue using proper labels and instructions for form controls');
      recommendations.push('Test forms with screen readers to verify accessibility');
    }

    if (labelsAnalysis.requiredControls > labelsAnalysis.requiredControlsWithInstructions) {
      recommendations.push('Clearly indicate required fields in labels or instructions');
    }

    const specializedControls = labelsAnalysis.formControls.filter(c => 
      ['email', 'tel', 'url', 'password'].includes(c.type)
    );
    if (specializedControls.length > 0) {
      recommendations.push('Provide format instructions for specialized input types');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private generateBeforeExample(control: FormControlAnalysis): string {
    if (control.type === 'password') {
      return '<input type="password">';
    } else if (control.type === 'email') {
      return '<input type="email">';
    } else if (control.element === 'select') {
      return '<select><option>Choose option</option></select>';
    } else {
      return `<input type="${control.type}">`;
    }
  }

  private generateAfterExample(control: FormControlAnalysis): string {
    if (control.type === 'password') {
      return `<label for="pwd">Password (required)</label>
<input type="password" id="pwd" required aria-describedby="pwd-help">
<div id="pwd-help">Must be at least 8 characters</div>`;
    } else if (control.type === 'email') {
      return `<label for="email">Email Address</label>
<input type="email" id="email" aria-describedby="email-help">
<div id="email-help">Enter a valid <NAME_EMAIL></div>`;
    } else if (control.element === 'select') {
      return `<label for="country">Country</label>
<select id="country">
  <option value="">Choose a country</option>
  <option value="us">United States</option>
</select>`;
    } else {
      return `<label for="field">${control.type.charAt(0).toUpperCase() + control.type.slice(1)}</label>
<input type="${control.type}" id="field">`;
    }
  }
}
