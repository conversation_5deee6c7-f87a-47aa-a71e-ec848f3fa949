/**
 * WCAG Rule 16: Redundant Entry - 3.3.7
 * 85% Automated - Manual review for complex form flows
 */

import { Page } from 'puppeteer';
import {
  ManualReviewConfig,
  ManualReviewTemplate,
  ManualReviewItem,
} from '../utils/manual-review-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { ComponentLibraryDetector } from '../utils/component-library-detector';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface RedundantEntryConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableSemanticValidation?: boolean;
  enableFrameworkOptimization?: boolean;
  enableRedundantEntryPatternRecognition?: boolean;
  enableFormEfficiencyAssessment?: boolean;
  enableAISemanticValidation?: boolean;
  enableAdvancedFormDataAnalysis?: boolean;
}

export class RedundantEntryCheck {
  private checkTemplate = new ManualReviewTemplate();
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private componentLibraryDetector = ComponentLibraryDetector.getInstance();

  /**
   * Perform redundant entry check - 85% automated with enhanced evidence
   */
  async performCheck(config: RedundantEntryConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: RedundantEntryConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableSemanticValidation: true,
      enableFrameworkOptimization: true,
      enableRedundantEntryPatternRecognition: true,
      enableFormEfficiencyAssessment: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-016',
      'Redundant Entry',
      'understandable',
      0.07,
      'AA',
      enhancedConfig,
      this.executeRedundantEntryCheck.bind(this),
      true, // Requires browser
      true, // Manual review for complex form flows
    );

    // Enhanced evidence standardization with form redundancy analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-016',
        ruleName: 'Redundant Entry',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'form-redundancy-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          autocompleteAnalysis: true,
          duplicateFieldDetection: true,
          multiStepFormAnalysis: true,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 40,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive redundant entry analysis
   */
  private async executeRedundantEntryCheck(page: Page, _config: ManualReviewConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    // Analyze form autocomplete attributes
    const autocompleteAnalysis = await this.analyzeAutocompleteAttributes(page);

    // Analyze duplicate form fields
    const duplicateFieldAnalysis = await this.analyzeDuplicateFields(page);

    // Analyze multi-step forms
    const multiStepAnalysis = await this.analyzeMultiStepForms(page);

    // Combine all analyses
    const allAnalyses = [autocompleteAnalysis, duplicateFieldAnalysis, multiStepAnalysis];

    let totalChecks = 0;
    let passedChecks = 0;
    let manualReviewCount = 0;

    allAnalyses.forEach((analysis) => {
      totalChecks += analysis.totalChecks;
      passedChecks += analysis.passedChecks;
      manualReviewCount += analysis.manualReviewItems.length;

      evidence.push(...analysis.evidence);
      issues.push(...analysis.issues);
      recommendations.push(...analysis.recommendations);
      manualReviewItems.push(...analysis.manualReviewItems);
    });

    // Calculate automated score
    const automatedChecks = totalChecks - manualReviewCount;
    const automatedScore =
      automatedChecks > 0 ? Math.round((passedChecks / automatedChecks) * 100) : 100;

    // Add summary evidence
    evidence.unshift({
      type: 'text',
      description: 'Redundant entry analysis summary',
      value: `${passedChecks}/${automatedChecks} checks pass automated tests, ${manualReviewCount} require manual review`,
      severity: automatedScore >= 90 ? 'info' : automatedScore >= 70 ? 'warning' : 'error',
    });

    return {
      automatedScore,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
      manualReviewItems,
      automationRate: 0.85,
    };
  }

  /**
   * Analyze autocomplete attributes for reducing redundant entry
   */
  private async analyzeAutocompleteAttributes(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      function generateSelector(element: Element, index: number): string {
        if (element.id) {
          return `#${element.id}`;
        }

        if (element.className) {
          const classes = element.className
            .toString()
            .split(' ')
            .filter((c) => c.length > 0);
          if (classes.length > 0) {
            return `${element.tagName.toLowerCase()}.${classes.join('.')}`;
          }
        }

        return `${element.tagName.toLowerCase()}:nth-of-type(${index + 1})`;
      }

      // Valid autocomplete values for common fields
      const autocompleteValues = {
        name: [
          'name',
          'given-name',
          'family-name',
          'additional-name',
          'honorific-prefix',
          'honorific-suffix',
        ],
        email: ['email'],
        phone: ['tel', 'tel-country-code', 'tel-national', 'tel-area-code', 'tel-local'],
        address: [
          'street-address',
          'address-line1',
          'address-line2',
          'address-level1',
          'address-level2',
          'postal-code',
          'country',
          'country-name',
        ],
        organization: ['organization', 'organization-title'],
        payment: [
          'cc-name',
          'cc-given-name',
          'cc-family-name',
          'cc-number',
          'cc-exp',
          'cc-exp-month',
          'cc-exp-year',
          'cc-csc',
          'cc-type',
        ],
        authentication: ['username', 'current-password', 'new-password'],
      };

      function getExpectedAutocomplete(input: HTMLInputElement): string[] {
        const type = input.type.toLowerCase();
        const name = input.name.toLowerCase();
        const id = input.id.toLowerCase();
        const placeholder = input.placeholder?.toLowerCase() || '';

        // Check by input type
        if (type === 'email') return autocompleteValues.email;
        if (type === 'tel') return autocompleteValues.phone;
        if (type === 'password') return autocompleteValues.authentication;

        // Check by name/id patterns
        if (name.includes('email') || id.includes('email') || placeholder.includes('email')) {
          return autocompleteValues.email;
        }
        if (
          name.includes('phone') ||
          name.includes('tel') ||
          id.includes('phone') ||
          placeholder.includes('phone')
        ) {
          return autocompleteValues.phone;
        }
        if (name.includes('name') || id.includes('name') || placeholder.includes('name')) {
          return autocompleteValues.name;
        }
        if (name.includes('address') || id.includes('address') || placeholder.includes('address')) {
          return autocompleteValues.address;
        }
        if (
          name.includes('zip') ||
          name.includes('postal') ||
          id.includes('zip') ||
          placeholder.includes('postal')
        ) {
          return ['postal-code'];
        }
        if (name.includes('country') || id.includes('country') || placeholder.includes('country')) {
          return ['country', 'country-name'];
        }

        return [];
      }

      const formInputs = Array.from(
        document.querySelectorAll('input:not([type="hidden"]), select, textarea'),
      );
      let totalChecks = 0;
      let passedChecks = 0;

      if (formInputs.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      formInputs.forEach((input, index) => {
        const element = input as HTMLInputElement;
        const selector = generateSelector(element, index);
        const expectedAutocomplete = getExpectedAutocomplete(element);

        if (expectedAutocomplete.length > 0) {
          totalChecks++;
          const autocomplete = element.getAttribute('autocomplete');

          if (autocomplete && expectedAutocomplete.includes(autocomplete)) {
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Field has appropriate autocomplete attribute',
              value: `autocomplete="${autocomplete}"`,
              selector,
              severity: 'info',
            });
          } else if (autocomplete) {
            // Has autocomplete but not the expected value
            evidence.push({
              type: 'text',
              description: 'Field has autocomplete but may not be optimal',
              value: `Current: "${autocomplete}", Expected: ${expectedAutocomplete.join(' or ')}`,
              selector,
              severity: 'warning',
            });
            passedChecks++; // Still counts as having autocomplete
          } else {
            issues.push(`Field missing autocomplete attribute: ${selector}`);
            recommendations.push(`Add autocomplete="${expectedAutocomplete[0]}" to ${selector}`);
          }
        } else {
          passedChecks++; // Fields that don't need autocomplete pass this check
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze for duplicate form fields within the same page
   */
  private async analyzeDuplicateFields(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      const formInputs = Array.from(
        document.querySelectorAll('input:not([type="hidden"]), select, textarea'),
      );
      let totalChecks = 0;
      let passedChecks = 0;

      if (formInputs.length === 0) {
        return {
          totalChecks: 0,
          passedChecks: 0,
          evidence,
          issues,
          recommendations,
          manualReviewItems,
        };
      }

      // Group fields by their apparent purpose
      const fieldGroups: { [key: string]: Element[] } = {};

      formInputs.forEach((input) => {
        const element = input as HTMLInputElement;
        const type = element.type.toLowerCase();
        const name = element.name.toLowerCase();
        const id = element.id.toLowerCase();
        const placeholder = element.placeholder?.toLowerCase() || '';

        // Create a key based on the field's apparent purpose
        let purposeKey = '';

        if (
          type === 'email' ||
          name.includes('email') ||
          id.includes('email') ||
          placeholder.includes('email')
        ) {
          purposeKey = 'email';
        } else if (
          type === 'tel' ||
          name.includes('phone') ||
          name.includes('tel') ||
          id.includes('phone') ||
          placeholder.includes('phone')
        ) {
          purposeKey = 'phone';
        } else if (
          name.includes('firstname') ||
          name.includes('first-name') ||
          id.includes('firstname') ||
          placeholder.includes('first name')
        ) {
          purposeKey = 'firstname';
        } else if (
          name.includes('lastname') ||
          name.includes('last-name') ||
          id.includes('lastname') ||
          placeholder.includes('last name')
        ) {
          purposeKey = 'lastname';
        } else if (
          name.includes('address') ||
          id.includes('address') ||
          placeholder.includes('address')
        ) {
          purposeKey = 'address';
        } else if (
          name.includes('zip') ||
          name.includes('postal') ||
          id.includes('zip') ||
          placeholder.includes('postal')
        ) {
          purposeKey = 'postal';
        }

        if (purposeKey) {
          if (!fieldGroups[purposeKey]) {
            fieldGroups[purposeKey] = [];
          }
          fieldGroups[purposeKey].push(element);
        }
      });

      // Check for duplicates
      Object.keys(fieldGroups).forEach((purposeKey) => {
        const fields = fieldGroups[purposeKey];
        totalChecks++;

        if (fields.length > 1) {
          // Multiple fields with same purpose - check if they're in different forms
          const forms = new Set(fields.map((field) => field.closest('form')));

          if (forms.size === 1) {
            // Same form has duplicate fields
            issues.push(`Duplicate ${purposeKey} fields found in same form`);
            recommendations.push(
              `Consider removing redundant ${purposeKey} fields or using autocomplete`,
            );

            evidence.push({
              type: 'text',
              description: 'Potential redundant entry detected',
              value: `Multiple ${purposeKey} fields in same form`,
              severity: 'warning',
            });
          } else {
            // Different forms - this might be acceptable
            passedChecks++;
            evidence.push({
              type: 'text',
              description: 'Similar fields in different forms',
              value: `${purposeKey} fields found in ${forms.size} different forms`,
              severity: 'info',
            });
          }
        } else {
          passedChecks++;
          evidence.push({
            type: 'text',
            description: 'No duplicate fields detected',
            value: `Single ${purposeKey} field found`,
            severity: 'info',
          });
        }
      });

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }

  /**
   * Analyze multi-step forms for redundant entry
   */
  private async analyzeMultiStepForms(page: Page): Promise<{
    totalChecks: number;
    passedChecks: number;
    evidence: WcagEvidence[];
    issues: string[];
    recommendations: string[];
    manualReviewItems: ManualReviewItem[];
  }> {
    return await page.evaluate(() => {
      const evidence: Array<{
        type:
          | 'text'
          | 'image'
          | 'code'
          | 'measurement'
          | 'interaction'
          | 'info'
          | 'warning'
          | 'error';
        description: string;
        value: string;
        selector?: string;
        severity?: 'info' | 'warning' | 'error' | 'critical';
      }> = [];
      const issues: string[] = [];
      const recommendations: string[] = [];
      const manualReviewItems: Array<{
        selector: string;
        description: string;
        automatedFindings: string;
        reviewRequired: string;
        priority: 'high' | 'medium' | 'low';
        estimatedTime: number;
      }> = [];

      // Look for indicators of multi-step forms
      const stepIndicators = Array.from(
        document.querySelectorAll(
          '.step, .wizard-step, [class*="step"], [class*="wizard"], .progress, .breadcrumb, [aria-label*="step"]',
        ),
      );

      const nextButtons = Array.from(
        document.querySelectorAll('button[type="submit"], input[type="submit"], button, a'),
      ).filter((btn) => {
        const text = btn.textContent?.toLowerCase() || '';
        return text.includes('next') || text.includes('continue') || text.includes('submit');
      });

      const totalChecks = 1;
      let passedChecks = 0;

      if (stepIndicators.length > 0 || nextButtons.length > 0) {
        // Potential multi-step form detected
        manualReviewItems.push({
          selector: 'body',
          description: 'Multi-step form redundant entry verification needed',
          automatedFindings: `Found ${stepIndicators.length} step indicators and ${nextButtons.length} navigation buttons`,
          reviewRequired:
            "Test the complete form flow to ensure users don't need to re-enter information across steps",
          priority: 'medium',
          estimatedTime: 8,
        });

        evidence.push({
          type: 'text',
          description: 'Potential multi-step form detected',
          value: 'Manual testing required for complete flow analysis',
          severity: 'info',
        });
      } else {
        passedChecks++;
        evidence.push({
          type: 'text',
          description: 'No multi-step form indicators found',
          value: 'Single-page form or no complex form flow detected',
          severity: 'info',
        });
      }

      return { totalChecks, passedChecks, evidence, issues, recommendations, manualReviewItems };
    });
  }
}
