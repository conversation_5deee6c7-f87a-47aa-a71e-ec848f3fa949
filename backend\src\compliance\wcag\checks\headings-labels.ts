/**
 * WCAG-036: Headings and Labels Check
 * Success Criterion: 2.4.6 Headings and Labels (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { LayoutAnalyzer } from '../utils/layout-analyzer';
import { AdvancedLayoutAnalyzer } from '../utils/advanced-layout-analyzer';
import EnhancedColorAnalyzer from '../utils/enhanced-color-analyzer';
import { WideGamutColorAnalyzer } from '../utils/wide-gamut-color-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { ContentQualityAnalyzer } from '../utils/content-quality-analyzer';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { WcagEvidence } from '../types';
import { WcagEvidenceEnhanced, WcagCheckResultEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface HeadingElement {
  level: number;
  text: string;
  selector: string;
  isEmpty: boolean;
  isDescriptive: boolean;
  isGeneric: boolean;
  wordCount: number;
  issues: string[];
}

interface LabelElement {
  type: 'label' | 'legend' | 'aria-label' | 'aria-labelledby';
  text: string;
  selector: string;
  associatedControl?: string;
  isEmpty: boolean;
  isDescriptive: boolean;
  isGeneric: boolean;
  wordCount: number;
  issues: string[];
}

interface HeadingsLabelsAnalysis {
  headings: HeadingElement[];
  labels: LabelElement[];
  totalHeadings: number;
  totalLabels: number;
  descriptiveHeadings: number;
  descriptiveLabels: number;
  emptyHeadings: number;
  emptyLabels: number;
  genericHeadings: number;
  genericLabels: number;
  headingsWithIssues: number;
  labelsWithIssues: number;
}

export interface HeadingsLabelsConfig extends EnhancedCheckConfig {
  enableAISemanticValidation?: boolean;
  enableContentQualityAnalysis?: boolean;
  enableAccessibilityPatterns?: boolean;
}

export class HeadingsLabelsCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private layoutAnalyzer = new LayoutAnalyzer();
  private advancedLayoutAnalyzer = AdvancedLayoutAnalyzer.getAdvancedInstance();
  private enhancedColorAnalyzer = EnhancedColorAnalyzer.getInstance();
  private wideGamutAnalyzer = WideGamutColorAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private contentQualityAnalyzer = ContentQualityAnalyzer.getInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();
  private advancedPatternDetector = AdvancedPatternDetector.getInstance();
  private patternRecognitionEngine = PatternRecognitionEngine.getInstance();

  // Generic/non-descriptive text patterns
  private readonly genericPatterns = [
    /^(heading|title|label|text|content|section|item|element)$/i,
    /^(click here|read more|more|link|button)$/i,
    /^(untitled|no title|default|placeholder)$/i,
    /^(h[1-6]|heading [1-6])$/i,
    /^(label|field|input|form)$/i
  ];

  // Common non-descriptive words
  private readonly nonDescriptiveWords = [
    'here', 'there', 'this', 'that', 'these', 'those',
    'click', 'read', 'see', 'view', 'go', 'get',
    'more', 'less', 'other', 'some', 'any', 'all'
  ];

  async performCheck(config: HeadingsLabelsConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: HeadingsLabelsConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'supplement',
        maxExecutionTime: 5000,
      },
      enableAISemanticValidation: true,
      enableContentQualityAnalysis: true,
      enableAccessibilityPatterns: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-036',
      'Headings and Labels',
      'operable',
      0.0815,
      'AA',
      enhancedConfig,
      this.executeHeadingsLabelsCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with heading/label structure analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-036',
        ruleName: 'Headings and Labels',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.85,
          checkType: 'content-structure-analysis',
          headingAnalysis: true,
          labelAnalysis: true,
          aiSemanticValidation: enhancedConfig.enableAISemanticValidation,
          contentQualityAnalysis: enhancedConfig.enableContentQualityAnalysis,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.75,
        maxEvidenceItems: 50,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'headings-labels-analysis',
        confidence: 0.80,
        additionalData: {
          checkType: 'content-accessibility',
          automationLevel: 'high',
        },
      },
    };
  }

  private async executeHeadingsLabelsCheck(
    page: Page,
    config: HeadingsLabelsConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Enhanced headings and labels analysis using AdvancedLayoutAnalyzer
    try {
      const headingsAnalysis = await this.advancedLayoutAnalyzer.analyzeResponsiveLayout(page);

      // Add enhanced evidence from advanced headings and labels analysis
      evidence.push({
        type: 'info',
        description: 'Advanced headings and labels structure analysis with semantic validation',
        element: 'headings-labels',
        value: JSON.stringify({
          overallScore: headingsAnalysis.overallScore,
          criticalIssues: headingsAnalysis.criticalIssues,
          recommendations: headingsAnalysis.recommendations,
          performanceMetrics: headingsAnalysis.performanceMetrics,
        }),
        severity: headingsAnalysis.criticalIssues.length > 0 ? 'error' : 'info',
      });

      // Collect issues and recommendations from advanced analysis
      if (headingsAnalysis.criticalIssues.length > 0) {
        issues.push(...headingsAnalysis.criticalIssues);
        recommendations.push(...headingsAnalysis.recommendations);
      }

    } catch (error) {
      console.warn('Advanced headings and labels analysis failed, falling back to basic analysis:', error);
    }

    // Analyze headings and labels for descriptiveness - Basic fallback analysis
    const headingsLabelsAnalysis = await page.evaluate(() => {
      const genericPatterns = [
        /^(heading|title|label|text|content|section|item|element)$/i,
        /^(click here|read more|more|link|button)$/i,
        /^(untitled|no title|default|placeholder)$/i,
        /^(h[1-6]|heading [1-6])$/i,
        /^(label|field|input|form)$/i
      ];

      const nonDescriptiveWords = [
        'here', 'there', 'this', 'that', 'these', 'those',
        'click', 'read', 'see', 'view', 'go', 'get',
        'more', 'less', 'other', 'some', 'any', 'all'
      ];

      // Helper function to analyze text descriptiveness
      function analyzeTextDescriptiveness(text: string): {
        isEmpty: boolean;
        isDescriptive: boolean;
        isGeneric: boolean;
        wordCount: number;
        issues: string[];
      } {
        const trimmedText = text.trim();
        const isEmpty = trimmedText.length === 0;
        const wordCount = trimmedText.split(/\s+/).filter(word => word.length > 0).length;
        const issues: string[] = [];

        if (isEmpty) {
          issues.push('Empty text');
          return { isEmpty: true, isDescriptive: false, isGeneric: false, wordCount: 0, issues };
        }

        // Check for generic patterns
        const isGeneric = genericPatterns.some(pattern => pattern.test(trimmedText));
        if (isGeneric) {
          issues.push('Generic or non-descriptive text');
        }

        // Check for non-descriptive words
        const words = trimmedText.toLowerCase().split(/\s+/);
        const nonDescriptiveCount = words.filter(word => 
          nonDescriptiveWords.includes(word)
        ).length;

        if (nonDescriptiveCount > 0 && nonDescriptiveCount >= wordCount * 0.5) {
          issues.push('Contains mostly non-descriptive words');
        }

        // Check length and specificity
        if (wordCount === 1 && !isGeneric) {
          issues.push('Very short, may not be descriptive enough');
        }

        if (wordCount > 10) {
          issues.push('Very long, may be too verbose');
        }

        const isDescriptive = !isGeneric && issues.length === 0 && wordCount >= 2;

        return { isEmpty, isDescriptive, isGeneric, wordCount, issues };
      }

      // Analyze headings
      const headingElements = document.querySelectorAll('h1, h2, h3, h4, h5, h6');
      const headings: HeadingElement[] = [];

      headingElements.forEach((heading, index) => {
        const level = parseInt(heading.tagName.charAt(1));
        const text = heading.textContent || '';
        const analysis = analyzeTextDescriptiveness(text);

        headings.push({
          level,
          text,
          selector: `${heading.tagName.toLowerCase()}:nth-of-type(${index + 1})`,
          isEmpty: analysis.isEmpty,
          isDescriptive: analysis.isDescriptive,
          isGeneric: analysis.isGeneric,
          wordCount: analysis.wordCount,
          issues: analysis.issues,
        });
      });

      // Analyze labels
      const labelElements = document.querySelectorAll('label');
      const legendElements = document.querySelectorAll('legend');
      const ariaLabelElements = document.querySelectorAll('[aria-label]');
      const ariaLabelledbyElements = document.querySelectorAll('[aria-labelledby]');
      
      const labels: LabelElement[] = [];

      // Process label elements
      labelElements.forEach((label, index) => {
        const text = label.textContent || '';
        const analysis = analyzeTextDescriptiveness(text);
        const forAttr = label.getAttribute('for');
        const associatedControl = forAttr ? `#${forAttr}` : 'nested input';

        labels.push({
          type: 'label',
          text,
          selector: `label:nth-of-type(${index + 1})`,
          associatedControl,
          isEmpty: analysis.isEmpty,
          isDescriptive: analysis.isDescriptive,
          isGeneric: analysis.isGeneric,
          wordCount: analysis.wordCount,
          issues: analysis.issues,
        });
      });

      // Process legend elements
      legendElements.forEach((legend, index) => {
        const text = legend.textContent || '';
        const analysis = analyzeTextDescriptiveness(text);

        labels.push({
          type: 'legend',
          text,
          selector: `legend:nth-of-type(${index + 1})`,
          associatedControl: 'fieldset',
          isEmpty: analysis.isEmpty,
          isDescriptive: analysis.isDescriptive,
          isGeneric: analysis.isGeneric,
          wordCount: analysis.wordCount,
          issues: analysis.issues,
        });
      });

      // Process aria-label elements
      ariaLabelElements.forEach((element, index) => {
        const text = element.getAttribute('aria-label') || '';
        const analysis = analyzeTextDescriptiveness(text);

        labels.push({
          type: 'aria-label',
          text,
          selector: `[aria-label]:nth-of-type(${index + 1})`,
          associatedControl: element.tagName.toLowerCase(),
          isEmpty: analysis.isEmpty,
          isDescriptive: analysis.isDescriptive,
          isGeneric: analysis.isGeneric,
          wordCount: analysis.wordCount,
          issues: analysis.issues,
        });
      });

      // Process aria-labelledby elements (check referenced elements)
      ariaLabelledbyElements.forEach((element, index) => {
        const labelledbyId = element.getAttribute('aria-labelledby');
        const referencedElement = labelledbyId ? document.getElementById(labelledbyId) : null;
        const text = referencedElement?.textContent || '';
        const analysis = analyzeTextDescriptiveness(text);

        if (referencedElement) {
          labels.push({
            type: 'aria-labelledby',
            text,
            selector: `[aria-labelledby]:nth-of-type(${index + 1})`,
            associatedControl: element.tagName.toLowerCase(),
            isEmpty: analysis.isEmpty,
            isDescriptive: analysis.isDescriptive,
            isGeneric: analysis.isGeneric,
            wordCount: analysis.wordCount,
            issues: analysis.issues,
          });
        }
      });

      const totalHeadings = headings.length;
      const totalLabels = labels.length;
      const descriptiveHeadings = headings.filter(h => h.isDescriptive).length;
      const descriptiveLabels = labels.filter(l => l.isDescriptive).length;
      const emptyHeadings = headings.filter(h => h.isEmpty).length;
      const emptyLabels = labels.filter(l => l.isEmpty).length;
      const genericHeadings = headings.filter(h => h.isGeneric).length;
      const genericLabels = labels.filter(l => l.isGeneric).length;
      const headingsWithIssues = headings.filter(h => h.issues.length > 0).length;
      const labelsWithIssues = labels.filter(l => l.issues.length > 0).length;

      return {
        headings,
        labels,
        totalHeadings,
        totalLabels,
        descriptiveHeadings,
        descriptiveLabels,
        emptyHeadings,
        emptyLabels,
        genericHeadings,
        genericLabels,
        headingsWithIssues,
        labelsWithIssues,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = headingsLabelsAnalysis.totalHeadings + headingsLabelsAnalysis.totalLabels;

    if (totalElements === 0) {
      // No headings or labels found
      evidence.push({
        type: 'info',
        description: 'No headings or labels found to analyze',
        value: 'Page contains no headings or form labels',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            totalElements: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Ensure any future headings and labels are descriptive and clear'],
      };
    }

    // Analyze headings with issues
    const problematicHeadings = headingsLabelsAnalysis.headings.filter(h => h.issues.length > 0);
    if (problematicHeadings.length > 0) {
      const headingFailureRate = problematicHeadings.length / headingsLabelsAnalysis.totalHeadings;
      score = Math.max(score - Math.round(headingFailureRate * 50), 0);

      problematicHeadings.forEach((heading) => {
        issues.push(`Heading H${heading.level}: ${heading.issues.join(', ')}`);
        
        evidence.push({
          type: heading.isEmpty ? 'error' : 'warning',
          description: `Heading with descriptiveness issues`,
          value: `H${heading.level} heading: "${heading.text}" - ${heading.issues.join(', ')}`,
          selector: heading.selector,
          elementCount: 1,
          affectedSelectors: [heading.selector],
          severity: heading.isEmpty ? 'error' : 'warning',
          fixExample: {
            before: this.generateHeadingBeforeExample(heading),
            after: this.generateHeadingAfterExample(heading),
            description: 'Make headings descriptive and specific to their content',
            codeExample: `
<!-- Bad: Generic headings -->
<h1>Welcome</h1>
<h2>Section</h2>
<h3>More</h3>

<!-- Good: Descriptive headings -->
<h1>Welcome to Our Online Store</h1>
<h2>Featured Products</h2>
<h3>Customer Reviews</h3>

<!-- Bad: Empty heading -->
<h2></h2>

<!-- Good: Meaningful heading -->
<h2>Contact Information</h2>
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/headings-and-labels.html',
              'https://webaim.org/techniques/headings/',
              'https://www.w3.org/WAI/tutorials/page-structure/headings/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              headingLevel: heading.level,
              headingText: heading.text,
              wordCount: heading.wordCount,
              isEmpty: heading.isEmpty,
              isGeneric: heading.isGeneric,
              issues: heading.issues,
            },
          },
        });
      });
    }

    // Analyze labels with issues
    const problematicLabels = headingsLabelsAnalysis.labels.filter(l => l.issues.length > 0);
    if (problematicLabels.length > 0) {
      const labelFailureRate = problematicLabels.length / headingsLabelsAnalysis.totalLabels;
      score = Math.max(score - Math.round(labelFailureRate * 50), 0);

      problematicLabels.forEach((label) => {
        issues.push(`Label (${label.type}): ${label.issues.join(', ')}`);
        
        evidence.push({
          type: label.isEmpty ? 'error' : 'warning',
          description: `Label with descriptiveness issues`,
          value: `${label.type}: "${label.text}" - ${label.issues.join(', ')}`,
          selector: label.selector,
          elementCount: 1,
          affectedSelectors: [label.selector],
          severity: label.isEmpty ? 'error' : 'warning',
          fixExample: {
            before: this.generateLabelBeforeExample(label),
            after: this.generateLabelAfterExample(label),
            description: 'Make labels descriptive and specific to their purpose',
            codeExample: `
<!-- Bad: Generic labels -->
<label for="field1">Field</label>
<input type="text" id="field1">

<label for="input2">Input</label>
<input type="email" id="input2">

<!-- Good: Descriptive labels -->
<label for="firstName">First Name</label>
<input type="text" id="firstName">

<label for="emailAddress">Email Address</label>
<input type="email" id="emailAddress">

<!-- Bad: Empty label -->
<label for="phone"></label>
<input type="tel" id="phone">

<!-- Good: Clear label -->
<label for="phone">Phone Number</label>
<input type="tel" id="phone">
            `,
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/headings-and-labels.html',
              'https://webaim.org/techniques/forms/controls',
              'https://www.w3.org/WAI/tutorials/forms/labels/'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              labelType: label.type,
              labelText: label.text,
              wordCount: label.wordCount,
              isEmpty: label.isEmpty,
              isGeneric: label.isGeneric,
              associatedControl: label.associatedControl,
              issues: label.issues,
            },
          },
        });
      });
    }

    // Add positive evidence for good headings and labels
    const goodElements = headingsLabelsAnalysis.descriptiveHeadings + headingsLabelsAnalysis.descriptiveLabels;
    if (goodElements > 0) {
      evidence.push({
        type: 'info',
        description: 'Descriptive headings and labels found',
        value: `${headingsLabelsAnalysis.descriptiveHeadings} descriptive headings, ${headingsLabelsAnalysis.descriptiveLabels} descriptive labels`,
        selector: 'h1, h2, h3, h4, h5, h6, label',
        elementCount: goodElements,
        affectedSelectors: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'label'],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: goodElements,
          checkSpecificData: {
            descriptiveHeadings: headingsLabelsAnalysis.descriptiveHeadings,
            descriptiveLabels: headingsLabelsAnalysis.descriptiveLabels,
          },
        },
      });
    }

    // Add summary evidence
    evidence.push({
      type: score >= 80 ? 'info' : 'warning',
      description: 'Headings and labels analysis summary',
      value: `${totalElements} elements: ${headingsLabelsAnalysis.totalHeadings} headings (${headingsLabelsAnalysis.headingsWithIssues} with issues), ${headingsLabelsAnalysis.totalLabels} labels (${headingsLabelsAnalysis.labelsWithIssues} with issues)`,
      selector: 'h1, h2, h3, h4, h5, h6, label, legend',
      elementCount: totalElements,
      affectedSelectors: ['h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'label', 'legend'],
      severity: score >= 80 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: totalElements,
        checkSpecificData: {
          totalHeadings: headingsLabelsAnalysis.totalHeadings,
          totalLabels: headingsLabelsAnalysis.totalLabels,
          descriptiveHeadings: headingsLabelsAnalysis.descriptiveHeadings,
          descriptiveLabels: headingsLabelsAnalysis.descriptiveLabels,
          headingsWithIssues: headingsLabelsAnalysis.headingsWithIssues,
          labelsWithIssues: headingsLabelsAnalysis.labelsWithIssues,
          emptyHeadings: headingsLabelsAnalysis.emptyHeadings,
          emptyLabels: headingsLabelsAnalysis.emptyLabels,
          descriptiveRate: totalElements > 0 
            ? ((headingsLabelsAnalysis.descriptiveHeadings + headingsLabelsAnalysis.descriptiveLabels) / totalElements * 100).toFixed(1)
            : '0',
        },
      },
    });

    // Generate recommendations
    if (problematicHeadings.length > 0 || problematicLabels.length > 0) {
      recommendations.push('Make all headings and labels descriptive and specific');
      recommendations.push('Avoid generic text like "Section", "Field", or "Click here"');
      recommendations.push('Ensure headings clearly describe the content that follows');
      recommendations.push('Make form labels clearly indicate what information is required');
    } else {
      recommendations.push('Continue using descriptive headings and labels');
      recommendations.push('Test headings and labels with screen reader users');
    }

    if (headingsLabelsAnalysis.emptyHeadings > 0 || headingsLabelsAnalysis.emptyLabels > 0) {
      recommendations.push('Remove empty headings and labels or provide meaningful content');
    }

    if (headingsLabelsAnalysis.genericHeadings > 0 || headingsLabelsAnalysis.genericLabels > 0) {
      recommendations.push('Replace generic headings and labels with specific, descriptive text');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private generateHeadingBeforeExample(heading: HeadingElement): string {
    if (heading.isEmpty) {
      return `<h${heading.level}></h${heading.level}>`;
    } else if (heading.isGeneric) {
      return `<h${heading.level}>${heading.text}</h${heading.level}>`;
    } else {
      return `<h${heading.level}>${heading.text}</h${heading.level}>`;
    }
  }

  private generateHeadingAfterExample(heading: HeadingElement): string {
    if (heading.isEmpty) {
      return `<h${heading.level}>Descriptive Section Title</h${heading.level}>`;
    } else if (heading.isGeneric) {
      return `<h${heading.level}>Specific Content Description</h${heading.level}>`;
    } else {
      return `<h${heading.level}>Improved: ${heading.text}</h${heading.level}>`;
    }
  }

  private generateLabelBeforeExample(label: LabelElement): string {
    if (label.type === 'label') {
      return label.isEmpty 
        ? '<label for="field"></label>'
        : `<label for="field">${label.text}</label>`;
    } else if (label.type === 'legend') {
      return label.isEmpty 
        ? '<legend></legend>'
        : `<legend>${label.text}</legend>`;
    } else {
      return label.isEmpty 
        ? '<button aria-label=""></button>'
        : `<button aria-label="${label.text}"></button>`;
    }
  }

  private generateLabelAfterExample(label: LabelElement): string {
    if (label.type === 'label') {
      return '<label for="field">Descriptive Field Name</label>';
    } else if (label.type === 'legend') {
      return '<legend>Clear Fieldset Description</legend>';
    } else {
      return '<button aria-label="Specific Action Description"></button>';
    }
  }
}
