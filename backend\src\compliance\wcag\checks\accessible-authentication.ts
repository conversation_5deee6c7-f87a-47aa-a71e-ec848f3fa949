/**
 * WCAG-022: Accessible Authentication (Minimum) Check (3.3.8 Level AA)
 * 50% Automated - Requires manual review for complex authentication flows
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { ManualReviewItem } from '../utils/manual-review-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

export interface AuthenticationElement {
  selector: string;
  type: 'login' | 'password' | 'captcha' | 'mfa' | 'biometric' | 'security-question' | 'other';
  inputType: string;
  hasAlternative: boolean;
  requiresMemory: boolean;
  requiresCognitive: boolean;
  description: string;
}

export interface AuthenticationFlow {
  steps: AuthenticationElement[];
  hasAccessibleAlternative: boolean;
  cognitiveLoad: 'low' | 'medium' | 'high';
  memoryRequirements: string[];
  recommendations: string[];
}

export interface AccessibleAuthenticationConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableSemanticValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableCognitiveLoadAssessment?: boolean;
  enableAlternativeMethodValidation?: boolean;
  enableAISemanticValidation?: boolean;
  enableAdvancedCognitiveAnalysis?: boolean;
}

export class AccessibleAuthenticationCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();
  private formAccessibilityAnalyzer = FormAccessibilityAnalyzer.getInstance();
  private aiSemanticValidator = AISemanticValidator.getAIInstance();
  private accessibilityPatternLibrary = AccessibilityPatternLibrary.getInstance();

  /**
   * Perform accessible authentication check - 50% automated with enhanced evidence
   */
  async performCheck(config: AccessibleAuthenticationConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: AccessibleAuthenticationConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableSemanticValidation: true,
      enableAccessibilityPatterns: true,
      enableCognitiveLoadAssessment: true,
      enableAlternativeMethodValidation: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-022',
      'Accessible Authentication (Minimum)',
      'understandable',
      0.05,
      'AA',
      enhancedConfig,
      this.executeAccessibleAuthenticationCheck.bind(this),
      true, // Requires browser
      true, // Manual review required
    );

    // Enhanced evidence standardization with authentication flow analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-022',
        ruleName: 'Accessible Authentication (Minimum)',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.5,
          checkType: 'authentication-flow-analysis',
          manualReviewRequired: result.manualReviewItems?.length > 0,
          authenticationElementDetection: true,
          cognitiveLoadAssessment: enhancedConfig.enableCognitiveLoadAssessment,
          alternativeMethodValidation: enhancedConfig.enableAlternativeMethodValidation,
          formAccessibilityAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
          accessibilityPatterns: enhancedConfig.enableAccessibilityPatterns,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.7,
        maxEvidenceItems: 25,
      }
    );

    return {
      ...result,
      evidence: enhancedEvidence,
    };
  }

  /**
   * Execute comprehensive accessible authentication analysis
   */
  private async executeAccessibleAuthenticationCheck(page: Page, config: AccessibleAuthenticationConfig) {
    const evidence: WcagEvidence[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const manualReviewItems: ManualReviewItem[] = [];

    try {
      // Detect authentication elements
      const authElements = await this.detectAuthenticationElements(page);

      // Analyze authentication flows
      const authFlows = await this.analyzeAuthenticationFlows(page, authElements);

      // Check for cognitive function tests
      const cognitiveTests = await this.detectCognitiveFunctionTests(page);

      // Evaluate accessibility alternatives
      const alternatives = await this.evaluateAccessibilityAlternatives(page, authElements);

      let totalChecks = 0;
      let passedChecks = 0;

      // Evaluate each authentication element
      for (const element of authElements) {
        totalChecks++;

        const elementEvidence: WcagEvidence = {
          type: 'interaction',
          description: `Authentication element: ${element.type}`,
          value: element.selector,
          severity: element.requiresMemory || element.requiresCognitive ? 'error' : 'info',
        };
        evidence.push(elementEvidence);

        if (element.type === 'captcha' && !element.hasAlternative) {
          issues.push(`CAPTCHA found without accessible alternative: ${element.selector}`);
          recommendations.push('Provide alternative authentication method for CAPTCHA');
        } else if (element.type === 'security-question' && !element.hasAlternative) {
          issues.push(`Security question requires memory without alternative: ${element.selector}`);
          recommendations.push('Provide alternative authentication that does not rely on memory');
        } else {
          passedChecks++;
        }
      }

      // Evaluate cognitive function tests
      for (const test of cognitiveTests) {
        totalChecks++;

        if (!test.hasAlternative) {
          issues.push(`Cognitive function test without alternative: ${test.description}`);
          recommendations.push(
            'Provide authentication method that does not require cognitive function test',
          );

          manualReviewItems.push({
            selector: test.selector,
            description: 'Cognitive function test in authentication',
            automatedFindings: `Found ${test.type} that may require cognitive abilities`,
            reviewRequired:
              'Verify if alternative authentication methods are available that do not rely on cognitive function tests',
            priority: 'high',
            estimatedTime: 10,
          });
        } else {
          passedChecks++;
        }
      }

      // Check for authentication flows
      for (const flow of authFlows) {
        totalChecks++;

        if (flow.cognitiveLoad === 'high' && !flow.hasAccessibleAlternative) {
          issues.push('High cognitive load authentication without accessible alternative');
          recommendations.push('Provide simpler authentication method or clear instructions');

          manualReviewItems.push({
            selector: 'body',
            description: 'High cognitive load authentication flow',
            automatedFindings: `Authentication requires: ${flow.memoryRequirements.join(', ')}`,
            reviewRequired:
              'Manually test authentication flow for users with cognitive disabilities',
            priority: 'high',
            estimatedTime: 15,
          });
        } else {
          passedChecks++;
        }
      }

      // Add general manual review items for complex authentication
      if (authElements.length > 0) {
        manualReviewItems.push({
          selector: 'form[action*="login"], form[action*="auth"], input[type="password"]',
          description: 'Authentication mechanism accessibility',
          automatedFindings: `Found ${authElements.length} authentication elements`,
          reviewRequired:
            'Manually verify that authentication does not rely solely on cognitive function tests like remembering passwords, solving puzzles, or identifying objects',
          priority: 'medium',
          estimatedTime: 20,
        });
      }

      // Check for biometric alternatives
      const biometricElements = authElements.filter((el) => el.type === 'biometric');
      if (biometricElements.length > 0) {
        manualReviewItems.push({
          selector: biometricElements[0].selector,
          description: 'Biometric authentication accessibility',
          automatedFindings: 'Biometric authentication detected',
          reviewRequired:
            'Verify that biometric authentication has accessible alternatives for users who cannot use biometric features',
          priority: 'medium',
          estimatedTime: 10,
        });
      }

      // Add evidence for alternatives found
      if (alternatives.length > 0) {
        evidence.push({
          type: 'info',
          description: 'Accessible authentication alternatives found',
          value: `${alternatives.length} alternative methods detected`,
          severity: 'info',
        });
      }

      // If no authentication found, note it
      if (authElements.length === 0) {
        evidence.push({
          type: 'info',
          description: 'No authentication mechanisms detected on this page',
          value: 'Page may not require authentication',
          severity: 'info',
        });
        totalChecks = 1;
        passedChecks = 1;
      }

      return {
        automatedScore: Math.round((passedChecks / Math.max(totalChecks, 1)) * 100),
        maxScore: 100,
        evidence,
        issues,
        recommendations,
        manualReviewItems,
        automationRate: 0.5, // 50% automation rate
      };
    } catch (error) {
      logger.error('Error in accessible authentication check:', {
        error: error instanceof Error ? error.message : String(error),
      });
      return {
        automatedScore: 0,
        maxScore: 100,
        evidence: [
          {
            type: 'error' as const,
            description: 'Error during authentication analysis',
            value: error instanceof Error ? error.message : 'Unknown error',
            severity: 'error' as const,
          },
        ],
        issues: ['Failed to analyze authentication accessibility'],
        recommendations: ['Review authentication mechanisms manually for accessibility compliance'],
        manualReviewItems: [],
        automationRate: 0.5,
      };
    }
  }

  /**
   * Detect authentication elements on the page
   */
  private async detectAuthenticationElements(page: Page): Promise<AuthenticationElement[]> {
    return await page.evaluate(() => {
      const elements: AuthenticationElement[] = [];

      // Common authentication selectors
      const authSelectors = [
        'input[type="password"]',
        'input[name*="password"]',
        'input[name*="login"]',
        'input[name*="username"]',
        'input[name*="email"][type="email"]',
        '[class*="captcha"]',
        '[id*="captcha"]',
        'input[name*="captcha"]',
        '[class*="mfa"]',
        '[class*="2fa"]',
        'input[name*="code"]',
        'input[name*="token"]',
        '[class*="biometric"]',
        '[class*="fingerprint"]',
        '[class*="face-id"]',
      ];

      authSelectors.forEach((selector) => {
        const foundElements = document.querySelectorAll(selector);
        foundElements.forEach((element) => {
          const el = element as HTMLElement;
          let type: AuthenticationElement['type'] = 'other';
          let requiresMemory = false;
          let requiresCognitive = false;

          // Determine element type
          if (selector.includes('password')) {
            type = 'password';
            requiresMemory = true;
          } else if (selector.includes('captcha')) {
            type = 'captcha';
            requiresCognitive = true;
          } else if (
            selector.includes('mfa') ||
            selector.includes('2fa') ||
            selector.includes('code')
          ) {
            type = 'mfa';
            requiresMemory = true;
          } else if (
            selector.includes('biometric') ||
            selector.includes('fingerprint') ||
            selector.includes('face')
          ) {
            type = 'biometric';
          } else if (
            selector.includes('login') ||
            selector.includes('username') ||
            selector.includes('email')
          ) {
            type = 'login';
          }

          // Check for alternatives
          const hasAlternative = this.checkForAlternatives(el, type);

          elements.push({
            selector: this.getElementSelector(el),
            type,
            inputType: (el as HTMLInputElement).type || 'unknown',
            hasAlternative,
            requiresMemory,
            requiresCognitive,
            description: this.getElementDescription(el, type),
          });
        });
      });

      return elements;
    });
  }

  /**
   * Analyze authentication flows
   */
  private async analyzeAuthenticationFlows(
    page: Page,
    authElements: AuthenticationElement[],
  ): Promise<AuthenticationFlow[]> {
    return await page.evaluate((elements) => {
      const flows: AuthenticationFlow[] = [];

      // Group elements by form
      const forms = document.querySelectorAll('form');
      forms.forEach((form) => {
        const formElements = elements.filter((el) =>
          form.contains(document.querySelector(el.selector)),
        );

        if (formElements.length > 0) {
          const memoryRequirements = formElements
            .filter((el) => el.requiresMemory)
            .map((el) => el.description);

          const cognitiveLoad = this.assessCognitiveLoad(formElements);
          const hasAccessibleAlternative = this.checkFormAlternatives(form);

          flows.push({
            steps: formElements,
            hasAccessibleAlternative,
            cognitiveLoad,
            memoryRequirements,
            recommendations: this.generateFlowRecommendations(formElements, cognitiveLoad),
          });
        }
      });

      return flows;
    }, authElements);
  }

  /**
   * Detect cognitive function tests
   */
  private async detectCognitiveFunctionTests(page: Page): Promise<
    Array<{
      selector: string;
      type: string;
      description: string;
      hasAlternative: boolean;
    }>
  > {
    return await page.evaluate(() => {
      const tests: Array<{
        selector: string;
        type: string;
        description: string;
        hasAlternative: boolean;
      }> = [];

      // Look for common cognitive tests
      const cognitiveSelectors = [
        '[class*="puzzle"]',
        '[class*="riddle"]',
        '[class*="math"]',
        '[class*="calculation"]',
        '[id*="security-question"]',
        '[name*="security-question"]',
        'select[name*="question"]',
        'input[placeholder*="question"]',
      ];

      cognitiveSelectors.forEach((selector) => {
        const elements = document.querySelectorAll(selector);
        elements.forEach((element) => {
          const el = element as HTMLElement;
          tests.push({
            selector: this.getElementSelector(el),
            type: this.determineCognitiveTestType(el),
            description: this.getCognitiveTestDescription(el),
            hasAlternative: this.checkCognitiveAlternatives(el),
          });
        });
      });

      return tests;
    });
  }

  /**
   * Evaluate accessibility alternatives
   */
  private async evaluateAccessibilityAlternatives(
    page: Page,
    authElements: AuthenticationElement[],
  ): Promise<string[]> {
    return await page.evaluate((elements) => {
      const alternatives: string[] = [];

      // Look for common alternative authentication methods
      const alternativeSelectors = [
        '[class*="social-login"]',
        '[class*="oauth"]',
        '[class*="sso"]',
        '[class*="magic-link"]',
        '[class*="passwordless"]',
        'button[class*="google"]',
        'button[class*="facebook"]',
        'button[class*="microsoft"]',
        '[class*="remember-me"]',
        '[class*="stay-logged-in"]',
      ];

      alternativeSelectors.forEach((selector) => {
        const elements = document.querySelectorAll(selector);
        if (elements.length > 0) {
          alternatives.push(selector);
        }
      });

      return alternatives;
    }, authElements);
  }

  /**
   * Check for alternatives to authentication elements
   */
  private checkForAlternatives(element: Element, type: string): boolean {
    // Check for alternative authentication methods
    const form = element.closest('form');
    if (!form) return false;

    // Look for alternative login methods
    const alternatives = form.querySelectorAll(
      [
        '[data-auth-alternative]',
        '.social-login',
        '.oauth-login',
        '.sso-login',
        '[aria-label*="alternative"]',
        '[title*="alternative"]',
      ].join(','),
    );

    return alternatives.length > 0;
  }

  /**
   * Get element selector for identification
   */
  private getElementSelector(element: Element): string {
    if (element.id) return `#${element.id}`;
    if (element.className) return `.${element.className.split(' ')[0]}`;
    return element.tagName.toLowerCase();
  }

  /**
   * Get description of authentication element
   */
  private getElementDescription(element: Element, type: string): string {
    const label =
      element.getAttribute('aria-label') ||
      element.getAttribute('placeholder') ||
      element.getAttribute('title') ||
      `${type} field`;
    return `${type}: ${label}`;
  }

  /**
   * Assess cognitive load of form elements
   */
  private assessCognitiveLoad(elements: AuthenticationElement[]): 'low' | 'medium' | 'high' {
    let complexityScore = 0;

    elements.forEach((el) => {
      // Check for complex input types
      if (el.inputType === 'password') complexityScore += 1;
      if (el.type === 'password') complexityScore += 1;

      // Check for CAPTCHA or complex verification
      if (el.type === 'captcha' || el.description.toLowerCase().includes('captcha')) {
        complexityScore += 3;
      }

      // Check for security questions
      if (
        el.type === 'security-question' ||
        el.description.toLowerCase().includes('security question')
      ) {
        complexityScore += 2;
      }

      // Add score based on cognitive requirements
      if (el.requiresCognitive) complexityScore += 2;
      if (el.requiresMemory) complexityScore += 1;
    });

    if (complexityScore >= 4) return 'high';
    if (complexityScore >= 2) return 'medium';
    return 'low';
  }

  /**
   * Check form for accessible alternatives
   */
  private checkFormAlternatives(form: Element): boolean {
    // Look for multiple authentication options
    const authOptions = form.querySelectorAll(
      ['button[type="submit"]', '.auth-option', '.login-method', '[data-auth-type]'].join(','),
    );

    return authOptions.length > 1;
  }

  /**
   * Generate recommendations for authentication flow
   */
  private generateFlowRecommendations(
    elements: AuthenticationElement[],
    cognitiveLoad: string,
  ): string[] {
    const recommendations: string[] = [];

    if (cognitiveLoad === 'high') {
      recommendations.push(
        'Provide alternative authentication methods that do not require cognitive function tests',
      );
      recommendations.push('Consider implementing biometric authentication options');
    }

    if (cognitiveLoad === 'medium') {
      recommendations.push('Consider simplifying authentication requirements');
    }

    // Add specific recommendations based on element types
    const hasCaptcha = elements.some((el) => el.type === 'captcha');
    const hasMemoryRequirement = elements.some((el) => el.requiresMemory);

    if (hasCaptcha) {
      recommendations.push('Provide audio alternative for CAPTCHA');
    }

    if (hasMemoryRequirement) {
      recommendations.push('Consider password managers or alternative authentication methods');
    }

    recommendations.push('Ensure all authentication methods are accessible via keyboard');
    recommendations.push('Provide clear instructions for authentication process');

    return recommendations;
  }

  /**
   * Determine cognitive test type
   */
  private determineCognitiveTestType(element: Element): string {
    const text = element.textContent?.toLowerCase() || '';
    const className = element.className.toLowerCase();

    if (text.includes('captcha') || className.includes('captcha')) return 'CAPTCHA';
    if (text.includes('puzzle') || text.includes('challenge')) return 'Visual Puzzle';
    if (text.includes('security question')) return 'Security Question';
    if (text.includes('memory') || text.includes('remember')) return 'Memory Test';

    return 'Unknown Cognitive Test';
  }

  /**
   * Get cognitive test description
   */
  private getCognitiveTestDescription(element: Element): string {
    const type = this.determineCognitiveTestType(element);
    const label =
      element.getAttribute('aria-label') ||
      element.getAttribute('title') ||
      element.textContent?.substring(0, 50) ||
      'Cognitive test element';

    return `${type}: ${label}`;
  }

  /**
   * Check for cognitive alternatives
   */
  private checkCognitiveAlternatives(element: Element): boolean {
    const form = element.closest('form') || element.parentElement;
    if (!form) return false;

    // Look for alternative methods
    const alternatives = form.querySelectorAll(
      [
        '[data-alternative]',
        '.alternative-auth',
        '.skip-verification',
        '[aria-label*="skip"]',
        '[aria-label*="alternative"]',
      ].join(','),
    );

    return alternatives.length > 0;
  }
}

// Helper function to import logger
import logger from '../../../utils/logger';
