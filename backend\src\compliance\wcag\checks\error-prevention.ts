/**
 * WCAG-032: Error Prevention Check
 * Success Criterion: 3.3.4 Error Prevention (Legal, Financial, Data) (Level AA)
 * Enhanced with element counts and fix examples
 */

import { Page } from 'puppeteer';
import { CheckConfig } from '../utils/check-template';
import { EnhancedCheckTemplate, EnhancedCheckConfig } from '../utils/enhanced-check-template';
import { FormAccessibilityAnalyzer } from '../utils/form-accessibility-analyzer';
import { AccessibilityPatternLibrary } from '../utils/accessibility-pattern-library';
import { AISemanticValidator } from '../utils/ai-semantic-validator';
import { WcagEvidence } from '../types';
import { WcagCheckResultEnhanced, WcagEvidenceEnhanced } from '../types-enhanced';
import { EvidenceStandardizer } from '../utils/evidence-standardizer';
import SmartCache from '../utils/smart-cache';

interface ErrorPreventionAnalysis {
  criticalForms: Array<{
    selector: string;
    type: 'legal' | 'financial' | 'data' | 'other';
    hasConfirmation: boolean;
    hasReversible: boolean;
    hasChecking: boolean;
    preventionMethods: string[];
    submitButtons: number;
    confirmationElements: string[];
    checkingElements: string[];
    riskLevel: 'low' | 'medium' | 'high' | 'critical';
  }>;
  submitButtons: Array<{
    selector: string;
    text: string;
    formType: string;
    hasConfirmation: boolean;
    hasWarning: boolean;
    isDestructive: boolean;
  }>;
  confirmationDialogs: Array<{
    selector: string;
    text: string;
    hasCancel: boolean;
    hasConfirm: boolean;
    isModal: boolean;
  }>;
  totalCriticalForms: number;
  formsWithPrevention: number;
  formsWithoutPrevention: number;
}

export interface ErrorPreventionConfig extends EnhancedCheckConfig {
  enableFormAccessibilityAnalysis?: boolean;
  enableCriticalOperationDetection?: boolean;
  enablePreventionMethodValidation?: boolean;
  enableAccessibilityPatterns?: boolean;
  enableRiskAssessment?: boolean;
}

export class ErrorPreventionCheck {
  private enhancedTemplate = new EnhancedCheckTemplate();
  private smartCache = SmartCache.getInstance();

  // Keywords that indicate critical/sensitive operations
  private readonly criticalKeywords = {
    legal: ['agreement', 'contract', 'terms', 'legal', 'binding', 'consent', 'authorize'],
    financial: ['payment', 'purchase', 'buy', 'order', 'billing', 'credit', 'debit', 'transfer', 'withdraw'],
    data: ['delete', 'remove', 'clear', 'reset', 'erase', 'destroy', 'permanent', 'irreversible']
  };

  // Prevention method indicators
  private readonly preventionIndicators = {
    confirmation: ['confirm', 'verify', 'are you sure', 'proceed', 'continue', 'yes'],
    reversible: ['undo', 'cancel', 'reverse', 'rollback', 'restore'],
    checking: ['review', 'check', 'validate', 'preview', 'summary']
  };

  async performCheck(config: ErrorPreventionConfig): Promise<WcagCheckResultEnhanced> {
    // Enhanced configuration with utility integration
    const enhancedConfig: ErrorPreventionConfig = {
      ...config,
      enableUtilityIntegration: true,
      utilityConfig: {
        enablePatternValidation: true,
        enableCaching: true,
        enableGracefulFallback: true,
        integrationStrategy: 'enhance',
        maxExecutionTime: 5000,
      },
      enableFormAccessibilityAnalysis: true,
      enableCriticalOperationDetection: true,
      enablePreventionMethodValidation: true,
      enableAccessibilityPatterns: true,
      enableRiskAssessment: true,
    };

    const result = await this.enhancedTemplate.executeEnhancedCheck(
      'WCAG-032',
      'Error Prevention',
      'understandable',
      0.0815,
      'AA',
      enhancedConfig,
      this.executeErrorPreventionCheck.bind(this),
      true, // Requires browser
      false, // No manual review
    );

    // Enhanced evidence standardization with error prevention analysis
    const enhancedEvidence = await EvidenceStandardizer.standardizeEvidenceEnhanced(
      result.evidence,
      {
        ruleId: 'WCAG-032',
        ruleName: 'Error Prevention',
        scanDuration: result.executionTime,
        elementsAnalyzed: result.evidence.length,
        checkSpecificData: {
          automationRate: 0.75,
          checkType: 'error-prevention-analysis',
          formAnalysis: enhancedConfig.enableFormAccessibilityAnalysis,
          criticalOperationDetection: enhancedConfig.enableCriticalOperationDetection,
          preventionMethodValidation: enhancedConfig.enablePreventionMethodValidation,
          riskAssessment: enhancedConfig.enableRiskAssessment,
        },
      },
      {
        enableAdvancedSelectors: true,
        enableContextAnalysis: true,
        enablePerformanceOptimization: true,
        evidenceQualityThreshold: 0.8,
        maxEvidenceItems: 30,
      }
    );
    const totalElements = enhancedEvidence.reduce((sum, ev) => sum + (ev.elementCount || 0), 0);
    const failedElements = enhancedEvidence.filter(ev => ev.severity === 'error').length;

    return {
      ...result,
      evidence: enhancedEvidence,
      elementCounts: {
        total: totalElements,
        failed: failedElements,
        passed: totalElements - failedElements,
      },
      performance: {
        scanDuration: result.executionTime,
        elementsAnalyzed: totalElements,
      },
      checkMetadata: {
        version: '1.0.0',
        algorithm: 'error-prevention-analysis',
        confidence: 0.70,
        additionalData: {
          checkType: 'form-safety',
          automationLevel: 'medium-high',
        },
      },
    };
  }

  private async executeErrorPreventionCheck(
    page: Page,
    config: ErrorPreventionConfig
  ): Promise<{
    score: number;
    maxScore: number;
    evidence: WcagEvidenceEnhanced[];
    issues: string[];
    recommendations: string[];
  }> {
    const evidence: WcagEvidenceEnhanced[] = [];
    const issues: string[] = [];
    const recommendations: string[] = [];
    const startTime = Date.now();

    // Analyze error prevention mechanisms
    const preventionAnalysis = await page.evaluate(() => {
      const forms = document.querySelectorAll('form');
      const criticalForms: ErrorPreventionAnalysis['criticalForms'] = [];
      const submitButtons: ErrorPreventionAnalysis['submitButtons'] = [];
      const confirmationDialogs: ErrorPreventionAnalysis['confirmationDialogs'] = [];

      // Keywords for critical operations
      const criticalKeywords = {
        legal: ['agreement', 'contract', 'terms', 'legal', 'binding', 'consent', 'authorize'],
        financial: ['payment', 'purchase', 'buy', 'order', 'billing', 'credit', 'debit', 'transfer', 'withdraw'],
        data: ['delete', 'remove', 'clear', 'reset', 'erase', 'destroy', 'permanent', 'irreversible']
      };

      const preventionIndicators = {
        confirmation: ['confirm', 'verify', 'are you sure', 'proceed', 'continue', 'yes'],
        reversible: ['undo', 'cancel', 'reverse', 'rollback', 'restore'],
        checking: ['review', 'check', 'validate', 'preview', 'summary']
      };

      forms.forEach((form, index) => {
        const formText = form.textContent?.toLowerCase() || '';
        const formHTML = form.innerHTML.toLowerCase();
        
        // Determine form type and risk level
        let formType: 'legal' | 'financial' | 'data' | 'other' = 'other';
        let riskLevel: 'low' | 'medium' | 'high' | 'critical' = 'low';

        // Check for critical keywords
        for (const [type, keywords] of Object.entries(criticalKeywords)) {
          if (keywords.some(keyword => formText.includes(keyword) || formHTML.includes(keyword))) {
            formType = type as 'legal' | 'financial' | 'data';
            riskLevel = type === 'data' ? 'critical' : type === 'financial' ? 'high' : 'medium';
            break;
          }
        }

        // Only analyze critical forms
        if (formType !== 'other') {
          // Check for prevention mechanisms
          const hasConfirmation = preventionIndicators.confirmation.some(indicator => 
            formText.includes(indicator) || formHTML.includes(indicator)
          );

          const hasReversible = preventionIndicators.reversible.some(indicator => 
            formText.includes(indicator) || formHTML.includes(indicator)
          );

          const hasChecking = preventionIndicators.checking.some(indicator => 
            formText.includes(indicator) || formHTML.includes(indicator)
          );

          const preventionMethods: string[] = [];
          if (hasConfirmation) preventionMethods.push('confirmation');
          if (hasReversible) preventionMethods.push('reversible');
          if (hasChecking) preventionMethods.push('checking');

          // Find submit buttons
          const formSubmitButtons = form.querySelectorAll('input[type="submit"], button[type="submit"], button:not([type])');
          
          // Find confirmation elements
          const confirmationElements = Array.from(form.querySelectorAll('*')).filter(el => {
            const text = el.textContent?.toLowerCase() || '';
            return preventionIndicators.confirmation.some(indicator => text.includes(indicator));
          }).map(el => el.tagName.toLowerCase());

          // Find checking elements
          const checkingElements = Array.from(form.querySelectorAll('*')).filter(el => {
            const text = el.textContent?.toLowerCase() || '';
            return preventionIndicators.checking.some(indicator => text.includes(indicator));
          }).map(el => el.tagName.toLowerCase());

          criticalForms.push({
            selector: `form:nth-of-type(${index + 1})`,
            type: formType,
            hasConfirmation,
            hasReversible,
            hasChecking,
            preventionMethods,
            submitButtons: formSubmitButtons.length,
            confirmationElements,
            checkingElements,
            riskLevel,
          });

          // Analyze submit buttons
          formSubmitButtons.forEach((button, btnIndex) => {
            const buttonText = button.textContent?.toLowerCase() || '';
            const isDestructive = buttonText.includes('delete') || 
                                 buttonText.includes('remove') || 
                                 buttonText.includes('destroy');

            submitButtons.push({
              selector: `form:nth-of-type(${index + 1}) button:nth-of-type(${btnIndex + 1})`,
              text: button.textContent?.trim() || '',
              formType,
              hasConfirmation,
              hasWarning: buttonText.includes('warning') || buttonText.includes('caution'),
              isDestructive,
            });
          });
        }
      });

      // Find confirmation dialogs
      const dialogSelectors = ['[role="dialog"]', '.modal', '.confirmation', '.alert-dialog'];
      dialogSelectors.forEach(selector => {
        const dialogs = document.querySelectorAll(selector);
        dialogs.forEach((dialog, index) => {
          const text = dialog.textContent?.toLowerCase() || '';
          const hasCancel = text.includes('cancel') || dialog.querySelector('[data-dismiss], .cancel, .close') !== null;
          const hasConfirm = text.includes('confirm') || text.includes('yes') || text.includes('proceed');
          const isModal = dialog.getAttribute('role') === 'dialog' || dialog.classList.contains('modal');

          confirmationDialogs.push({
            selector: `${selector}:nth-of-type(${index + 1})`,
            text: dialog.textContent?.trim() || '',
            hasCancel,
            hasConfirm,
            isModal,
          });
        });
      });

      const totalCriticalForms = criticalForms.length;
      const formsWithPrevention = criticalForms.filter(form => form.preventionMethods.length > 0).length;
      const formsWithoutPrevention = totalCriticalForms - formsWithPrevention;

      return {
        criticalForms,
        submitButtons,
        confirmationDialogs,
        totalCriticalForms,
        formsWithPrevention,
        formsWithoutPrevention,
      };
    });

    const scanDuration = Date.now() - startTime;
    let score = 100;
    const totalElements = preventionAnalysis.totalCriticalForms;

    if (totalElements === 0) {
      // No critical forms found
      evidence.push({
        type: 'info',
        description: 'No critical forms requiring error prevention found',
        value: 'Page contains no legal, financial, or data modification forms',
        selector: 'body',
        elementCount: 0,
        affectedSelectors: [],
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: 0,
          checkSpecificData: {
            totalCriticalForms: 0,
          },
        },
      });

      return {
        score: 100,
        maxScore: 100,
        evidence,
        issues,
        recommendations: ['Ensure any future critical forms implement appropriate error prevention'],
      };
    }

    // Analyze forms without prevention mechanisms
    if (preventionAnalysis.formsWithoutPrevention > 0) {
      const failureRate = preventionAnalysis.formsWithoutPrevention / totalElements;
      score = Math.max(0, Math.round(100 * (1 - failureRate)));

      preventionAnalysis.criticalForms
        .filter(form => form.preventionMethods.length === 0)
        .forEach((form) => {
          issues.push(`${form.type} form without error prevention mechanisms`);
          
          evidence.push({
            type: 'error',
            description: `Critical ${form.type} form missing error prevention`,
            value: `${form.type} form with ${form.riskLevel} risk level lacks prevention mechanisms`,
            selector: form.selector,
            elementCount: 1,
            affectedSelectors: [form.selector],
            severity: form.riskLevel === 'critical' ? 'error' : 'warning',
            fixExample: {
              before: this.generateBeforeExample(form.type),
              after: this.generateAfterExample(form.type),
              description: `Add error prevention for ${form.type} operations`,
              codeExample: this.generateCodeExample(form.type),
              resources: [
                'https://www.w3.org/WAI/WCAG21/Understanding/error-prevention-legal-financial-data.html',
                'https://webaim.org/techniques/formvalidation/',
                'https://www.w3.org/WAI/tutorials/forms/validation/'
              ]
            },
            metadata: {
              scanDuration,
              elementsAnalyzed: 1,
              checkSpecificData: {
                formType: form.type,
                riskLevel: form.riskLevel,
                hasConfirmation: form.hasConfirmation,
                hasReversible: form.hasReversible,
                hasChecking: form.hasChecking,
                submitButtons: form.submitButtons,
              },
            },
          });
        });
    }

    // Check for forms with partial prevention
    const formsWithPartialPrevention = preventionAnalysis.criticalForms.filter(form => 
      form.preventionMethods.length > 0 && form.preventionMethods.length < 2 && form.riskLevel === 'critical'
    );

    if (formsWithPartialPrevention.length > 0) {
      score = Math.max(score - (formsWithPartialPrevention.length * 10), 0);
      
      formsWithPartialPrevention.forEach((form) => {
        issues.push(`Critical ${form.type} form has only partial error prevention`);
        
        evidence.push({
          type: 'warning',
          description: `Critical form with insufficient error prevention`,
          value: `${form.type} form has only ${form.preventionMethods.join(', ')} - needs additional mechanisms`,
          selector: form.selector,
          elementCount: 1,
          affectedSelectors: [form.selector],
          severity: 'warning',
          fixExample: {
            before: `Form with only ${form.preventionMethods[0]}`,
            after: 'Form with confirmation + checking/reversible action',
            description: 'Add additional error prevention mechanisms for critical operations',
            resources: [
              'https://www.w3.org/WAI/WCAG21/Understanding/error-prevention-legal-financial-data.html'
            ]
          },
          metadata: {
            scanDuration,
            elementsAnalyzed: 1,
            checkSpecificData: {
              formType: form.type,
              riskLevel: form.riskLevel,
              currentMethods: form.preventionMethods,
              needsAdditional: true,
            },
          },
        });
      });
    }

    // Add positive evidence for good prevention
    const formsWithGoodPrevention = preventionAnalysis.criticalForms.filter(form => 
      form.preventionMethods.length >= 2 || (form.preventionMethods.length >= 1 && form.riskLevel !== 'critical')
    );

    if (formsWithGoodPrevention.length > 0) {
      evidence.push({
        type: 'info',
        description: 'Forms with adequate error prevention found',
        value: `${formsWithGoodPrevention.length} critical forms have appropriate prevention mechanisms`,
        selector: 'form',
        elementCount: formsWithGoodPrevention.length,
        affectedSelectors: formsWithGoodPrevention.map(form => form.selector),
        severity: 'info',
        metadata: {
          scanDuration,
          elementsAnalyzed: formsWithGoodPrevention.length,
          checkSpecificData: {
            formsWithGoodPrevention: formsWithGoodPrevention.length,
            preventionMethods: formsWithGoodPrevention.map(form => form.preventionMethods),
          },
        },
      });
    }

    // Add summary evidence
    evidence.push({
      type: score >= 80 ? 'info' : 'warning',
      description: 'Error prevention analysis summary',
      value: `${totalElements} critical forms: ${preventionAnalysis.formsWithPrevention} with prevention, ${preventionAnalysis.formsWithoutPrevention} without`,
      selector: 'form',
      elementCount: totalElements,
      affectedSelectors: ['form'],
      severity: score >= 80 ? 'info' : 'warning',
      metadata: {
        scanDuration,
        elementsAnalyzed: totalElements,
        checkSpecificData: {
          totalCriticalForms: preventionAnalysis.totalCriticalForms,
          formsWithPrevention: preventionAnalysis.formsWithPrevention,
          formsWithoutPrevention: preventionAnalysis.formsWithoutPrevention,
          confirmationDialogs: preventionAnalysis.confirmationDialogs.length,
          preventionRate: totalElements > 0 
            ? (preventionAnalysis.formsWithPrevention / totalElements * 100).toFixed(1)
            : '0',
        },
      },
    });

    // Generate recommendations
    if (preventionAnalysis.formsWithoutPrevention > 0) {
      recommendations.push('Implement error prevention for all critical forms (legal, financial, data)');
      recommendations.push('Use confirmation dialogs for irreversible actions');
      recommendations.push('Provide review/checking mechanisms before submission');
      recommendations.push('Make actions reversible where possible');
    } else {
      recommendations.push('Continue implementing error prevention for critical operations');
      recommendations.push('Test prevention mechanisms with users');
    }

    if (formsWithPartialPrevention.length > 0) {
      recommendations.push('Add additional prevention mechanisms for critical operations');
      recommendations.push('Consider multiple prevention methods for high-risk actions');
    }

    return {
      score,
      maxScore: 100,
      evidence,
      issues,
      recommendations,
    };
  }

  private generateBeforeExample(formType: string): string {
    switch (formType) {
      case 'financial':
        return '<form><button type="submit">Complete Purchase</button></form>';
      case 'data':
        return '<form><button type="submit">Delete Account</button></form>';
      case 'legal':
        return '<form><button type="submit">Accept Agreement</button></form>';
      default:
        return '<form><button type="submit">Submit</button></form>';
    }
  }

  private generateAfterExample(formType: string): string {
    switch (formType) {
      case 'financial':
        return `<form>
  <button type="button" onclick="showConfirmation()">Complete Purchase</button>
</form>
<div id="confirmation" role="dialog" style="display: none;">
  <p>Confirm your purchase of $99.99?</p>
  <button onclick="submitPurchase()">Yes, Complete Purchase</button>
  <button onclick="hideConfirmation()">Cancel</button>
</div>`;
      case 'data':
        return `<form>
  <button type="button" onclick="showDeleteConfirmation()">Delete Account</button>
</form>
<div id="delete-confirmation" role="dialog" style="display: none;">
  <p>Are you sure? This action cannot be undone.</p>
  <input type="text" placeholder="Type 'DELETE' to confirm">
  <button onclick="deleteAccount()">Delete Account</button>
  <button onclick="hideConfirmation()">Cancel</button>
</div>`;
      case 'legal':
        return `<form>
  <div class="agreement-review">
    <h3>Agreement Summary</h3>
    <p>Please review the terms before accepting...</p>
  </div>
  <label>
    <input type="checkbox" required> I have read and agree to the terms
  </label>
  <button type="submit">Accept Agreement</button>
</form>`;
      default:
        return '<form><button type="submit" onclick="return confirm(\'Are you sure?\')">Submit</button></form>';
    }
  }

  private generateCodeExample(formType: string): string {
    return `
// JavaScript for ${formType} form error prevention
function showConfirmation() {
  document.getElementById('confirmation').style.display = 'block';
  document.getElementById('confirmation').focus();
}

function hideConfirmation() {
  document.getElementById('confirmation').style.display = 'none';
}

function submitForm() {
  // Additional validation before submission
  if (validateForm()) {
    document.forms[0].submit();
  }
}

// For reversible actions, provide undo functionality
function provideUndo(actionType) {
  const undoNotification = document.createElement('div');
  undoNotification.innerHTML = \`
    <p>\${actionType} completed. <button onclick="undoAction()">Undo</button></p>
  \`;
  document.body.appendChild(undoNotification);
  
  setTimeout(() => {
    undoNotification.remove();
  }, 10000); // 10 second undo window
}
    `;
  }
}
